import { Converter } from "@/components/converter";

export default function HomeEN() {
  return (
    <main className="flex min-h-screen w-full flex-col items-center justify-center bg-background p-4 sm:p-8">
      <h1 className="mb-4 text-center text-3xl font-bold md:text-4xl">
        Online CSV & TXT to Excel Converter
      </h1>
      <p className="mb-6 max-w-2xl text-center text-lg text-muted-foreground">
        Convert your CSV or TXT files to Excel (.xlsx) format easily and securely.
        Our free online tool supports multilingual data, preserves formatting, and
        ensures your data privacy. No registration required!
      </p>
      <Converter />
      <section className="mt-12 max-w-3xl w-full">
        <h2 className="mb-4 text-2xl font-bold text-center text-primary">
          Frequently Asked Questions (FAQ)
        </h2>
        <div className="mb-8 grid gap-4 md:grid-cols-2">
          <div className="rounded-lg border bg-card p-4 shadow-sm">
            <h3 className="font-semibold text-lg mb-2 text-accent-foreground">
              What is the CSV & TXT to Excel Converter?
            </h3>
            <p className="mb-2 text-muted-foreground">
              It is a free online tool that allows you to convert CSV or TXT files
              to Excel (XLSX) format easily and quickly, with full support for
              multilingual data and formatting preservation.
            </p>
          </div>
          <div className="rounded-lg border bg-card p-4 shadow-sm">
            <h3 className="font-semibold text-lg mb-2 text-accent-foreground">
              Is my data safe when using this tool?
            </h3>
            <p className="mb-2 text-muted-foreground">
              Yes, no data is stored on the server. All conversions are performed
              locally in your browser for complete privacy.
            </p>
          </div>
          <div className="rounded-lg border bg-card p-4 shadow-sm">
            <h3 className="font-semibold text-lg mb-2 text-accent-foreground">
              Does the tool support special characters and languages?
            </h3>
            <p className="mb-2 text-muted-foreground">
              Yes, the tool supports converting files containing any language or
              special characters without encoding issues.
            </p>
          </div>
          <div className="rounded-lg border bg-card p-4 shadow-sm">
            <h3 className="font-semibold text-lg mb-2 text-accent-foreground">
              Is there a file size limit?
            </h3>
            <p className="mb-2 text-muted-foreground">
              You can convert large files, but performance may vary depending on
              your device capabilities.
            </p>
          </div>
          <div className="rounded-lg border bg-card p-4 shadow-sm md:col-span-2">
            <h3 className="font-semibold text-lg mb-2 text-accent-foreground">
              Is the tool completely free?
            </h3>
            <p className="mb-2 text-muted-foreground">
              Yes, the tool is 100% free and does not require registration or any
              fees.
            </p>
          </div>
        </div>
      </section>
    </main>
  );
}
