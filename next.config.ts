import type {NextConfig} from 'next';

const nextConfig: NextConfig = {
  /* config options here */
  typescript: {
    ignoreBuildErrors: false, // تم التحسين: إزالة تجاهل أخطاء TypeScript
  },
  eslint: {
    ignoreDuringBuilds: false, // تم التحسين: إزالة تجاهل أخطاء ESLint
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'placehold.co',
        port: '',
        pathname: '/**',
      },
    ],
    formats: ['image/webp', 'image/avif'], // تحسين: دعم تنسيقات صور محسّنة
  },
  // تحسين: إضافة ضغط
  compress: true,
  // تحسين: إعدادات الأداء
  experimental: {
    scrollRestoration: true,
  },
  // تحسين: headers للأمان والأداء
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
    ];
  },
};

export default nextConfig;
