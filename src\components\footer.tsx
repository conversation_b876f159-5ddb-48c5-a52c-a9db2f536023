"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";

export default function Footer() {
  const pathname = usePathname();
  const currentLang = pathname.split("/")[1] === "fr" ? "fr" : (pathname.split("/")[1] === "ar" ? "ar" : "en");
  
  // تحديد ما إذا كانت اللغة العربية لإضافة دعم RTL
  const isRTL = currentLang === "ar";
  
  // تحديد الروابط بناءً على اللغة الحالية
  const getLocalizedLink = (enPath: string, frPath?: string, arPath?: string) => {
    if (currentLang === "fr" && frPath) return frPath;
    if (currentLang === "ar" && arPath) return arPath;
    return enPath;
  };
  
  // تحديد نص الرابط بناءً على اللغة الحالية
  const getLinkText = (en: string, fr: string, ar: string) => {
    if (currentLang === "fr") return fr;
    if (currentLang === "ar") return ar;
    return en;
  };

  return (
    <footer className="w-full bg-secondary border-t border-border py-6 px-4 mt-8" dir={isRTL ? "rtl" : "ltr"}>
      <div className="flex flex-col max-w-6xl mx-auto">
        {/* القسم الرئيسي من Footer */}
        <div className="flex flex-col md:flex-row items-center justify-between mb-6">
          <div className="text-sm text-secondary-foreground">
            © {new Date().getFullYear()} {isRTL ? "محول CSV إلى Excel" : "CSV to Excel Converter"}. {isRTL ? "جميع الحقوق محفوظة." : "All rights reserved."}
          </div>
          <nav className="flex gap-4 mt-2 md:mt-0">
            <Link
              href={getLocalizedLink("/en/about", "/fr/about", "/ar/about")}
              className="text-secondary-foreground hover:text-accent transition-colors"
            >
              {getLinkText("About", "À propos", "حول الموقع")}
            </Link>
            <Link
              href={getLocalizedLink("/en/privacy-policy", "/fr/privacy-policy", "/ar/privacy-policy")}
              className="text-secondary-foreground hover:text-accent transition-colors"
            >
              {getLinkText("Privacy Policy", "Politique de confidentialité", "سياسة الخصوصية")}
            </Link>
            <Link
              href={getLocalizedLink("/en/terms", "/fr/terms", "/ar/terms")}
              className="text-secondary-foreground hover:text-accent transition-colors"
            >
              {getLinkText("Terms", "Conditions", "الشروط")}
            </Link>
            <Link
              href={getLocalizedLink("/en/contact", "/fr/contact", "/ar/contact")}
              className="text-secondary-foreground hover:text-accent transition-colors"
            >
              {getLinkText("Contact", "Contact", "اتصل بنا")}
            </Link>
          </nav>
        </div>
        
        {/* قسم AdSense والـ Cookies */}
        <div className="border-t border-border pt-4">
          <h3 className="text-xs uppercase text-secondary-foreground/70 mb-2 text-center">
            {getLinkText("Advertising & Cookies Information", "Informations sur la publicité et les cookies", "معلومات الإعلانات وملفات تعريف الارتباط")}
          </h3>
          <nav className="flex flex-wrap justify-center gap-x-4 gap-y-2 text-xs">
            <Link
              href={getLocalizedLink("/en/advertising-policy", "/fr/politique-publicitaire", "/ar/advertising-policy")}
              className="text-secondary-foreground/80 hover:text-accent transition-colors"
            >
              {getLinkText("Advertising Policy", "Politique publicitaire", "سياسة الإعلانات")}
            </Link>
            <Link
              href={getLocalizedLink("/en/cookies-policy", "/fr/cookies", "/ar/cookies-policy")}
              className="text-secondary-foreground/80 hover:text-accent transition-colors"
            >
              {getLinkText("Cookies Policy", "Politique des cookies", "سياسة ملفات تعريف الارتباط")}
            </Link>
            <Link
              href={getLocalizedLink("/en/adsense-info", "/fr/adsense-info", "/ar/adsense-info")}
              className="text-secondary-foreground/80 hover:text-accent transition-colors"
            >
              {getLinkText("AdSense Information", "Informations AdSense", "معلومات AdSense")}
            </Link>
            <Link
              href={getLocalizedLink("/en/adsense-disclosure", "/fr/adsense-disclosure", "/ar/adsense-disclosure")}
              className="text-secondary-foreground/80 hover:text-accent transition-colors"
            >
              {getLinkText("AdSense Disclosure", "Divulgation AdSense", "إفصاح AdSense")}
            </Link>
          </nav>
        </div>
      </div>
    </footer>
  );
}
