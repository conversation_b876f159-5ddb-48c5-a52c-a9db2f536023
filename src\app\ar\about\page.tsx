export default function AboutAR() {
  return (
    <main className="max-w-3xl mx-auto p-6 direction-rtl">
      <h1 className="text-3xl font-bold mb-6 text-right">حول الموقع</h1>
      
      <div className="space-y-6 text-right">
        <p className="text-lg">
          مرحبًا بك في محول CSV و TXT إلى Excel - أداة مجانية عبر الإنترنت مصممة لتسهيل تحويل ملفاتك بسلاسة وأمان.
        </p>

        <section className="space-y-3">
          <h2 className="text-xl font-semibold">مهمتنا</h2>
          <p>
            مهمتنا هي توفير أداة تحويل ملفات سهلة الاستخدام ومجانية وآمنة للجميع. نعتقد أن التحويل بين تنسيقات الملفات يجب أن يكون عملية بسيطة وسريعة وآمنة، دون الحاجة إلى برامج معقدة أو تكاليف باهظة.
          </p>
        </section>

        <section className="space-y-3">
          <h2 className="text-xl font-semibold">ما يميزنا</h2>
          <div className="bg-card border rounded-lg p-5 shadow-sm">
            <ul className="space-y-3 list-disc mr-6">
              <li>
                <span className="font-medium">معالجة جانب العميل:</span> تتم معالجة جميع الملفات محليًا في متصفحك، مما يضمن خصوصية بياناتك وأمانها.
              </li>
              <li>
                <span className="font-medium">دعم اللغة العربية:</span> تم تصميم أداتنا خصيصًا لدعم اللغة العربية والنصوص ثنائية الاتجاه، بالإضافة إلى اللغات الأخرى.
              </li>
              <li>
                <span className="font-medium">سهولة الاستخدام:</span> واجهة بسيطة وسهلة الاستخدام تسمح لك بتحويل ملفاتك بنقرات قليلة.
              </li>
              <li>
                <span className="font-medium">مجاني تمامًا:</span> لا توجد رسوم أو اشتراكات خفية. أداتنا مجانية تمامًا للاستخدام.
              </li>
              <li>
                <span className="font-medium">لا يلزم التسجيل:</span> استخدم الأداة فورًا دون الحاجة إلى إنشاء حساب.
              </li>
            </ul>
          </div>
        </section>

        <section className="space-y-3">
          <h2 className="text-xl font-semibold">التقنيات المستخدمة</h2>
          <p>
            يعتمد محول CSV و TXT إلى Excel الخاص بنا على تقنيات الويب الحديثة لتوفير تجربة سلسة وسريعة:
          </p>
          <ul className="list-disc mr-6 space-y-1">
            <li>Next.js - إطار عمل React للتطبيقات المتطورة</li>
            <li>TypeScript - للتطوير الآمن والموثوق</li>
            <li>مكتبات JavaScript المتقدمة لمعالجة الملفات</li>
            <li>تصميم متجاوب يعمل على جميع الأجهزة</li>
          </ul>
        </section>

        <section className="space-y-3">
          <h2 className="text-xl font-semibold">فريقنا</h2>
          <p>
            تم تطوير هذا الموقع بواسطة فريق من المطورين ذوي الخبرة المتحمسين لإنشاء أدوات سهلة الاستخدام ومفيدة. نحن نسعى جاهدين لتحسين أداتنا باستمرار بناءً على ملاحظات المستخدمين والتقنيات الجديدة.
          </p>
        </section>

        <section className="space-y-3">
          <h2 className="text-xl font-semibold">تواصل معنا</h2>
          <p>
            نرحب بملاحظاتك واقتراحاتك! إذا كانت لديك أي أسئلة أو تعليقات أو اقتراحات لتحسين أداتنا، يرجى عدم التردد في <a href="/ar/contact" className="text-primary hover:underline">الاتصال بنا</a>.
          </p>
        </section>

        <div className="bg-primary/5 rounded-lg p-5 border">
          <h2 className="text-xl font-semibold mb-3">أهدافنا المستقبلية</h2>
          <p className="mb-3">
            نحن نعمل باستمرار على تحسين أداتنا وإضافة ميزات جديدة. بعض الميزات المستقبلية التي نخطط لإضافتها:
          </p>
          <ul className="list-disc mr-6 space-y-1">
            <li>دعم المزيد من تنسيقات الملفات</li>
            <li>خيارات متقدمة للتخصيص أثناء التحويل</li>
            <li>تحسينات أداء لمعالجة الملفات الكبيرة بشكل أسرع</li>
            <li>توفير المزيد من اللغات والدعم متعدد اللغات</li>
          </ul>
        </div>
      </div>
    </main>
  );
}