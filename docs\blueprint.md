# **App Name**: CSV to Excel Converter

## Core Features:

- CSV Input: Text area for pasting CSV data.
- Conversion Trigger: Convert to Excel (.xlsx) button.
- Data Conversion: Client-side CSV to Excel conversion using SheetJS (xlsx).
- File Download: Direct download of the generated .xlsx file.
- Arabic Support: UTF-8 encoding support for Arabic characters.

## Style Guidelines:

- Primary color: Light blue (#ADD8E6), giving a sense of calmness and reliability for data processing.
- Background color: Very light blue (#F0F8FF), nearly white, to keep the interface clean and focused on the content.
- Accent color: A slightly darker blue (#77B5FE) for interactive elements to draw the user's attention.
- Font: 'Inter' (sans-serif) for both headlines and body text, providing a clean and readable experience.
- Simple, flat icons to represent actions like 'convert' and 'download'.
- Clean and straightforward layout with a prominent text input area and a clear 'Convert' button.
- Subtle animation on button hover to provide feedback.