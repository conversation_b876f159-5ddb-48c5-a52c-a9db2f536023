{"name": "csv-excel-converter", "version": "1.0.0", "description": "Free online CSV to Excel converter with multilingual support", "keywords": ["csv", "excel", "converter", "xlsx", "online", "free", "multilingual", "arabic"], "homepage": "https://csv-excel-converter.web.app", "repository": {"type": "git", "url": "https://github.com/your-username/csv-excel-converter.git"}, "bugs": {"url": "https://github.com/your-username/csv-excel-converter/issues"}, "author": {"name": "CSV Excel Converter Team", "email": "<EMAIL>"}, "license": "MIT", "private": false, "scripts": {"dev": "next dev --turbopack -p 3000", "dev:debug": "next dev --turbopack -p 3000 --inspect", "genkit:dev": "genkit start -- tsx src/ai/dev.ts", "genkit:watch": "genkit start -- tsx --watch src/ai/dev.ts", "build": "next build", "build:analyze": "ANALYZE=true next build", "build:production": "NODE_ENV=production next build", "start": "next start", "start:production": "NODE_ENV=production next start -p 3000", "lint": "next lint", "lint:fix": "next lint --fix", "lint:strict": "next lint --max-warnings 0", "typecheck": "tsc --noEmit", "typecheck:watch": "tsc --noEmit --watch", "test": "echo \"Tests not configured yet\" && exit 0", "test:watch": "echo \"Tests not configured yet\" && exit 0", "clean": "rm -rf .next out dist", "clean:cache": "rm -rf .next/cache", "export": "next export", "sitemap": "next build && node scripts/generate-sitemap.js", "check-updates": "npx npm-check-updates", "security-audit": "npm audit --audit-level moderate", "postinstall": "echo \"Setup complete! Run 'npm run dev' to start development.\"", "prepare": "husky install || true"}, "dependencies": {"@genkit-ai/googleai": "^1.8.0", "@genkit-ai/next": "^1.8.0", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "firebase": "^11.9.1", "genkit": "^1.8.0", "lucide-react": "^0.475.0", "next": "15.3.3", "patch-package": "^8.0.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "recharts": "^2.15.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "genkit-cli": "^1.8.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}