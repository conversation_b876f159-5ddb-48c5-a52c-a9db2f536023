import { type MetadataRoute } from 'next'

// استخدام متغير البيئة NEXT_PUBLIC_SITE_URL للسهولة في تغيير عنوان الموقع
const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://csv-excel-converter.web.app'

export default function sitemap(): MetadataRoute.Sitemap {
  const lastModified = new Date()
  
  return [
    // الصفحة الرئيسية
    {
      url: baseUrl,
      lastModified,
      changeFrequency: 'weekly',
      priority: 1.0,
    },
    // الصفحات الإنجليزية
    {
      url: `${baseUrl}/en`,
      lastModified,
      changeFrequency: 'weekly',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/en/about`,
      lastModified,
      changeFrequency: 'monthly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}/en/contact`,
      lastModified,
      changeFrequency: 'monthly',
      priority: 0.6,
    },
    {
      url: `${baseUrl}/en/privacy-policy`,
      lastModified,
      changeFrequency: 'yearly',
      priority: 0.5,
    },
    {
      url: `${baseUrl}/en/terms`,
      lastModified,
      changeFrequency: 'yearly',
      priority: 0.5,
    },
    {
      url: `${baseUrl}/en/cookies`,
      lastModified,
      changeFrequency: 'yearly',
      priority: 0.4,
    },
    // صفحات AdSense الإنجليزية
    {
      url: `${baseUrl}/en/advertising-policy`,
      lastModified,
      changeFrequency: 'yearly',
      priority: 0.5,
    },
    {
      url: `${baseUrl}/en/cookies-policy`,
      lastModified,
      changeFrequency: 'yearly',
      priority: 0.5,
    },
    {
      url: `${baseUrl}/en/adsense-info`,
      lastModified,
      changeFrequency: 'yearly',
      priority: 0.5,
    },
    {
      url: `${baseUrl}/en/adsense-disclosure`,
      lastModified,
      changeFrequency: 'yearly',
      priority: 0.5,
    },
    // الصفحات الفرنسية
    {
      url: `${baseUrl}/fr`,
      lastModified,
      changeFrequency: 'weekly',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/fr/about`,
      lastModified,
      changeFrequency: 'monthly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}/fr/contact`,
      lastModified,
      changeFrequency: 'monthly',
      priority: 0.6,
    },
    {
      url: `${baseUrl}/fr/privacy-policy`,
      lastModified,
      changeFrequency: 'yearly',
      priority: 0.5,
    },
    {
      url: `${baseUrl}/fr/terms`,
      lastModified,
      changeFrequency: 'yearly',
      priority: 0.5,
    },
    {
      url: `${baseUrl}/fr/cookies`,
      lastModified,
      changeFrequency: 'yearly',
      priority: 0.4,
    },
    // صفحات AdSense الفرنسية
    {
      url: `${baseUrl}/fr/politique-publicitaire`,
      lastModified,
      changeFrequency: 'yearly',
      priority: 0.5,
    },
    // الصفحات العربية
    {
      url: `${baseUrl}/ar`,
      lastModified,
      changeFrequency: 'weekly',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/ar/about`,
      lastModified,
      changeFrequency: 'monthly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}/ar/contact`,
      lastModified,
      changeFrequency: 'monthly',
      priority: 0.6,
    },
    {
      url: `${baseUrl}/ar/privacy-policy`,
      lastModified,
      changeFrequency: 'yearly',
      priority: 0.5,
    },
    {
      url: `${baseUrl}/ar/terms`,
      lastModified,
      changeFrequency: 'yearly',
      priority: 0.5,
    },
    // إضافة صفحات المدونة العربية
    {
      url: `${baseUrl}/ar/blog/csv-formats`,
      lastModified,
      changeFrequency: 'monthly',
      priority: 0.8,
    },
    // إضافة أي صفحات مفقودة أخرى من هيكل الملفات
    {
      url: `${baseUrl}/accessibility-statement`,
      lastModified,
      changeFrequency: 'yearly',
      priority: 0.5,
    },
    {
      url: `${baseUrl}/accessibility-demo`,
      lastModified,
      changeFrequency: 'monthly',
      priority: 0.6,
    },
  ]
}
