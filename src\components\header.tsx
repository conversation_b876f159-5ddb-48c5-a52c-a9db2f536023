"use client";

import Link from "next/link";
import { useIsMobile } from "@/hooks/use-mobile";
import LanguageSwitcher from "./language-switcher";
import { Sheet, SheetTrigger, SheetContent } from "@/components/ui/sheet";
import { Menu } from "lucide-react";
import { usePathname } from "next/navigation";

export default function Header() {
  const isMobile = useIsMobile();
  const pathname = usePathname();
  const currentLang = pathname.split("/")[1] === "fr" ? "fr" : (pathname.split("/")[1] === "ar" ? "ar" : "en");
  
  // تحديد ما إذا كانت اللغة العربية لإضافة دعم RTL
  const isRTL = currentLang === "ar";
  
  // تحديد الروابط بناءً على اللغة الحالية
  const getLocalizedLink = (enPath: string, frPath?: string, arPath?: string) => {
    if (currentLang === "fr" && frPath) return frPath;
    if (currentLang === "ar" && arPath) return arPath;
    return enPath;
  };
  
  // تحديد نص الرابط بناءً على اللغة الحالية
  const getLinkText = (en: string, fr: string, ar: string) => {
    if (currentLang === "fr") return fr;
    if (currentLang === "ar") return ar;
    return en;
  };

  return (
    <header className={`w-full bg-primary border-b border-border shadow-sm py-3 px-4 flex items-center justify-between`} dir={isRTL ? "rtl" : "ltr"}>
      <Link 
        href="/" 
        className="text-2xl font-extrabold tracking-tight text-white drop-shadow-md hover:text-white relative z-10"
        style={{ textShadow: '0 0 5px rgba(0,0,0,0.3)' }}
      >
        {isRTL ? "محول CSV إلى Excel" : "CSV to Excel Converter"}
      </Link>
      <div className="flex items-center gap-2">
        {isMobile ? null : <LanguageSwitcher />}
        {isMobile ? (
          <Sheet>
            <SheetTrigger asChild>
              <button className={`${isRTL ? 'mr-2' : 'ml-2'} p-2 rounded-md text-primary-foreground hover:bg-primary/80 focus:outline-none`}>
                <Menu className="w-6 h-6" />
                <span className="sr-only">{isRTL ? "فتح القائمة" : "Open menu"}</span>
              </button>
            </SheetTrigger>
            <SheetContent side={isRTL ? "left" : "right"} dir={isRTL ? "rtl" : "ltr"}>
              <div className="mb-6">
                <LanguageSwitcher />
              </div>
              <nav className="flex flex-col gap-4 mt-2">
                <Link 
                  href={getLocalizedLink("/en/about", "/fr/about", "/ar/about")} 
                  className="text-black dark:text-white hover:text-accent transition-colors font-medium"
                >
                  {getLinkText("About", "À propos", "حول الموقع")}
                </Link>
                <Link 
                  href={getLocalizedLink("/en/contact", "/fr/contact", "/ar/contact")} 
                  className="text-black dark:text-white hover:text-accent transition-colors font-medium"
                >
                  {getLinkText("Contact", "Contact", "اتصل بنا")}
                </Link>
              </nav>
            </SheetContent>
          </Sheet>
        ) : (
          <nav className="flex gap-4">
            <Link 
              href={getLocalizedLink("/en/about", "/fr/about", "/ar/about")} 
              className="text-primary-foreground hover:text-accent transition-colors font-medium"
            >
              {getLinkText("About", "À propos", "حول الموقع")}
            </Link>
            <Link 
              href={getLocalizedLink("/en/contact", "/fr/contact", "/ar/contact")} 
              className="text-primary-foreground hover:text-accent transition-colors font-medium"
            >
              {getLinkText("Contact", "Contact", "اتصل بنا")}
            </Link>
          </nav>
        )}
      </div>
    </header>
  );
}
