"use client";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

const languages = [
  { code: "en", label: "EN" },
  { code: "fr", label: "Fr" },
  { code: "ar", label: "عربي" },
];

export default function LanguageSwitcher() {
  const pathname = usePathname();
  const router = useRouter();
  const [isChanging, setIsChanging] = useState(false);
  
  // تحديث التحقق من اللغة الحالية ليشمل العربية
  const currentPathSegment = pathname.split("/")[1];
  const currentLang = languages.some(l => l.code === currentPathSegment) 
    ? currentPathSegment 
    : "en";
    
  // تحديد ما إذا كانت اللغة العربية لإضافة دعم RTL
  const isRTL = currentLang === "ar";

  function switchLanguage(lang: string) {
    if (lang === currentLang || isChanging) return;
    
    setIsChanging(true);
    
    const segments = pathname.split("/").filter(Boolean);
    if (languages.some(l => l.code === segments[0])) {
      segments[0] = lang;
    } else {
      segments.unshift(lang);
    }
    
    // منع نقرات متعددة متتالية
    setTimeout(() => {
      router.push("/" + segments.join("/"));
    }, 50);
  }
  
  // إعادة تعيين حالة التغيير بعد التنقل
  useEffect(() => {
    setIsChanging(false);
  }, [pathname]);

  return (
    <div 
      className="flex gap-2 items-center justify-end relative z-50"
      dir={isRTL ? "rtl" : "ltr"}
      style={{
        pointerEvents: 'auto'
      }}
    >
      {(isRTL ? [...languages].reverse() : languages).map((lang) => (
        <button
          key={lang.code}
          onClick={() => switchLanguage(lang.code)}
          disabled={isChanging}
          className={`
            px-2 py-1 text-xs font-bold rounded relative z-50
            min-w-[36px] transition-all duration-200
            ${isChanging ? 'opacity-70' : 'opacity-100'}
            ${currentLang === lang.code 
              ? 'bg-blue-500 text-white border-blue-600 hover:bg-blue-600' 
              : 'bg-white text-gray-800 border border-gray-300 hover:bg-gray-100'}
          `}
          style={{
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
          }}
        >
          {lang.label}
        </button>
      ))}
    </div>
  );
}
