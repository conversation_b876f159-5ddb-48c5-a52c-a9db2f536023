export default function ContactAR() {
  return (
    <main className="max-w-3xl mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">اتصل بنا</h1>
      
      <div className="space-y-6">
        <p className="text-lg">
          نرحب بأسئلتك واقتراحاتك وملاحظاتك. يمكنك التواصل معنا باستخدام المعلومات أدناه أو ملء نموذج الاتصال.
        </p>

        <div className="bg-card border rounded-lg p-6 shadow-sm">
          <h2 className="text-xl font-semibold mb-4">نموذج الاتصال</h2>
          
          <form className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="name" className="block font-medium">
                الاسم
              </label>
              <input
                type="text"
                id="name"
                className="w-full border rounded-md p-2 bg-background"
                placeholder="أدخل اسمك"
              />
            </div>
            
            <div className="space-y-2">
              <label htmlFor="email" className="block font-medium">
                البريد الإلكتروني
              </label>
              <input
                type="email"
                id="email"
                className="w-full border rounded-md p-2 bg-background"
                placeholder="أدخل بريدك الإلكتروني"
              />
            </div>
            
            <div className="space-y-2">
              <label htmlFor="subject" className="block font-medium">
                الموضوع
              </label>
              <input
                type="text"
                id="subject"
                className="w-full border rounded-md p-2 bg-background"
                placeholder="أدخل موضوع رسالتك"
              />
            </div>
            
            <div className="space-y-2">
              <label htmlFor="message" className="block font-medium">
                الرسالة
              </label>
              <textarea
                id="message"
                className="w-full border rounded-md p-2 h-32 bg-background"
                placeholder="أدخل رسالتك هنا"
              />
            </div>
            
            <button
              type="submit"
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 font-medium"
            >
              إرسال
            </button>
          </form>
        </div>

        <div className="bg-card border rounded-lg p-6 shadow-sm">
          <h2 className="text-xl font-semibold mb-4">معلومات الاتصال</h2>
          
          <div className="space-y-3">
            <div>
              <h3 className="font-medium">البريد الإلكتروني:</h3>
              <p><EMAIL></p>
            </div>
            
            <div>
              <h3 className="font-medium">ساعات العمل:</h3>
              <p>الاثنين - الجمعة: 9:00 صباحًا - 5:00 مساءً</p>
            </div>
          </div>
        </div>

        <div className="bg-card border rounded-lg p-6 shadow-sm">
          <h2 className="text-xl font-semibold mb-4">الأسئلة الشائعة</h2>
          
          <div className="space-y-4">
            <div>
              <h3 className="font-medium">كم يكلف استخدام أداة التحويل؟</h3>
              <p>أداة التحويل الخاصة بنا مجانية تمامًا للاستخدام. لا توجد رسوم خفية أو اشتراكات.</p>
            </div>
            
            <div>
              <h3 className="font-medium">هل تحتفظون بنسخة من ملفاتي المحولة؟</h3>
              <p>لا، تتم معالجة جميع الملفات محليًا في متصفحك ولا يتم تحميلها إلى خوادمنا. نحن لا نحتفظ بأي نسخ من ملفاتك.</p>
            </div>
            
            <div>
              <h3 className="font-medium">هل أحتاج إلى إنشاء حساب لاستخدام الأداة؟</h3>
              <p>لا، لا يلزم التسجيل لاستخدام أداة التحويل الخاصة بنا.</p>
            </div>
          </div>
        </div>

      </div>
    </main>
  );
}