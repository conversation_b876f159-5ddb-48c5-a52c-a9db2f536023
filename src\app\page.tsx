import { Converter } from "@/components/converter";

// استخدام متغير البيئة NEXT_PUBLIC_SITE_URL للسهولة في تغيير عنوان الموقع
const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://csv-excel-converter.web.app';

// تحسين: Structured Data مباشرة في الكمبوننت
const structuredData = {
  "@context": "https://schema.org",
  "@graph": [
    {
      "@type": "WebSite",
      "@id": `${baseUrl}/#website`,
      url: baseUrl,
      name: "CSV to Excel Converter - Free Online Tool",
      description: "Convert CSV and TXT files to Excel (.xlsx) format online for free. Fast, secure conversion tool supporting multilingual data.",
      inLanguage: "en-US",
      potentialAction: [
        {
          "@type": "SearchAction",
          target: {
            "@type": "EntryPoint",
            urlTemplate: `${baseUrl}/?q={search_term_string}`
          },
          "query-input": "required name=search_term_string"
        }
      ]
    },
    {
      "@type": "WebApplication",
      "@id": `${baseUrl}/#webapp`,
      url: baseUrl,
      name: "CSV to Excel Converter",
      description: "Free online tool to convert CSV and TXT files to Excel format with multilingual support",
      applicationCategory: "UtilitiesApplication",
      operatingSystem: "All",
      offers: {
        "@type": "Offer",
        price: "0",
        priceCurrency: "USD"
      },
      featureList: [
        "Convert CSV to Excel",
        "Convert TXT to Excel",
        "Multilingual support",
        "No registration required",
        "Secure processing",
        "Fast conversion"
      ]
    },
    {
      "@type": "Organization",
      "@id": `${baseUrl}/#organization`,
      name: "CSV Excel Converter Team",
      url: baseUrl
    }
  ]
};

export default function Home() {
  return (
    <>
      {/* تحسين: Structured Data في مكان صحيح */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />
      
      <div className="flex min-h-screen w-full flex-col items-center justify-center bg-background p-4 sm:p-8">
        {/* تحسين: Header Section */}
        <header className="text-center mb-8 max-w-4xl">
          <h1 className="mb-4 text-center text-3xl font-bold md:text-4xl lg:text-5xl leading-tight">
            Free CSV & TXT to Excel Converter Online
          </h1>
          <p className="mb-6 max-w-2xl mx-auto text-center text-lg text-muted-foreground leading-relaxed">
            Convert your CSV or TXT files to Excel (.xlsx) format easily and
            securely. Our free online tool supports multilingual data including Arabic text,
            preserves formatting, and ensures your data privacy. No registration
            required!
          </p>
          
          {/* تحسين: Key Features */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6 text-sm">
            <div className="flex flex-col items-center p-3 bg-primary/5 rounded-lg">
              <span className="font-semibold text-primary">100% Free</span>
              <span className="text-muted-foreground">No hidden costs</span>
            </div>
            <div className="flex flex-col items-center p-3 bg-primary/5 rounded-lg">
              <span className="font-semibold text-primary">Secure</span>
              <span className="text-muted-foreground">No data stored</span>
            </div>
            <div className="flex flex-col items-center p-3 bg-primary/5 rounded-lg">
              <span className="font-semibold text-primary">Fast</span>
              <span className="text-muted-foreground">Instant conversion</span>
            </div>
            <div className="flex flex-col items-center p-3 bg-primary/5 rounded-lg">
              <span className="font-semibold text-primary">Multilingual</span>
              <span className="text-muted-foreground">Arabic support</span>
            </div>
          </div>
        </header>

        {/* تحسين: Converter Section */}
        <section aria-label="File Converter Tool" className="w-full max-w-4xl">
          <Converter />
        </section>

        {/* تحسين: Benefits Section */}
        <section className="mt-12 max-w-2xl text-center" aria-labelledby="benefits-heading">
          <h2 id="benefits-heading" className="mb-4 text-2xl font-semibold">
            Why Choose Our CSV to Excel Converter?
          </h2>
          <ul className="mx-auto list-inside list-disc text-left text-base space-y-2 bg-card p-6 rounded-lg border">
            <li className="flex items-start gap-2">
              <span className="text-green-500 mt-1">✓</span>
              <span>100% Free and secure – no data stored on our servers</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-green-500 mt-1">✓</span>
              <span>Supports CSV, TXT, and Excel (.xlsx) formats</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-green-500 mt-1">✓</span>
              <span>Fast conversion with no registration required</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-green-500 mt-1">✓</span>
              <span>Works perfectly on all devices and browsers</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-green-500 mt-1">✓</span>
              <span>Full support for Arabic and multilingual text</span>
            </li>
          </ul>
        </section>

        {/* تحسين: Articles Section */}
        <section className="mt-16 max-w-5xl w-full" aria-labelledby="articles-heading">
          <h2 id="articles-heading" className="mb-8 text-3xl font-bold text-center text-primary">
            Expert Guide: File Conversion and Data Management
          </h2>
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            <article className="rounded-lg border bg-card p-6 shadow-sm hover:shadow-md transition-shadow flex flex-col h-full">
              <h3 className="font-semibold text-xl mb-3 text-accent-foreground">
                How to Convert CSV Files to Excel with Multilingual Support
              </h3>
              <p className="mb-4 text-muted-foreground flex-1 leading-relaxed">
                Converting CSV files to Excel can be challenging when dealing
                with multilingual text due to encoding issues. With our tool, all
                characters including Arabic, Chinese, and European accents are preserved correctly,
                ensuring you can open the file in Excel without any display or data loss problems.
              </p>
              <div className="text-sm text-primary font-medium">
                Learn more about encoding →
              </div>
            </article>
            
            <article className="rounded-lg border bg-card p-6 shadow-sm hover:shadow-md transition-shadow flex flex-col h-full">
              <h3 className="font-semibold text-xl mb-3 text-accent-foreground">
                Best Practices for Organizing Your Data Before Conversion
              </h3>
              <p className="mb-4 text-muted-foreground flex-1 leading-relaxed">
                For optimal results, ensure your CSV or TXT data is well-organized
                with clear delimiters and consistent formatting. Remove unnecessary empty rows
                and ensure column headers are properly defined. This preparation makes
                the conversion process smoother and reduces errors significantly.
              </p>
              <div className="text-sm text-primary font-medium">
                Data preparation tips →
              </div>
            </article>
            
            <article className="rounded-lg border bg-card p-6 shadow-sm hover:shadow-md transition-shadow flex flex-col h-full">
              <h3 className="font-semibold text-xl mb-3 text-accent-foreground">
                Benefits of Using Online Conversion Tools vs Desktop Software
              </h3>
              <p className="mb-4 text-muted-foreground flex-1 leading-relaxed">
                Online conversion tools offer immediate access without software installation,
                automatic updates, and cross-platform compatibility. They're perfect for occasional use,
                save storage space, and work identically across Windows, Mac, and mobile devices.
                Plus, they're always up-to-date with the latest file format standards.
              </p>
              <div className="text-sm text-primary font-medium">
                Compare solutions →
              </div>
            </article>
          </div>
        </section>

        {/* تحسين: FAQ Section */}
        <section className="mt-16 max-w-4xl w-full" aria-labelledby="faq-heading">
          <h2 id="faq-heading" className="mb-8 text-2xl font-bold text-center">
            Frequently Asked Questions
          </h2>
          <div className="space-y-4">
            <details className="group bg-card border rounded-lg p-4">
              <summary className="font-semibold cursor-pointer">
                Is this CSV to Excel converter really free?
              </summary>
              <p className="mt-2 text-muted-foreground">
                Yes, our tool is completely free with no hidden charges, registration requirements, or usage limits.
              </p>
            </details>
            
            <details className="group bg-card border rounded-lg p-4">
              <summary className="font-semibold cursor-pointer">
                Do you store my files on your servers?
              </summary>
              <p className="mt-2 text-muted-foreground">
                No, all conversion happens in your browser. Your files never leave your device, ensuring complete privacy.
              </p>
            </details>
            
            <details className="group bg-card border rounded-lg p-4">
              <summary className="font-semibold cursor-pointer">
                What file formats are supported?
              </summary>
              <p className="mt-2 text-muted-foreground">
                We support CSV, TXT, and TSV input files, and convert them to Excel (.xlsx) format.
              </p>
            </details>
          </div>
        </section>
      </div>
    </>
  );
}
