# CSV to Excel Converter - Free Online Tool

A modern, fast, and secure web application for converting CSV and TXT files to Excel (.xlsx) format online. Built with Next.js 15, TypeScript, and Tailwind CSS.

## 🌟 Features

- **100% Free & Secure**: No registration required, no data stored on servers
- **Multilingual Support**: Full support for Arabic, Chinese, and other Unicode characters
- **Smart CSV Parsing**: Handles quoted fields, various delimiters, and complex data
- **File Upload & Paste**: Upload files or paste data directly
- **Real-time Preview**: See your data before conversion
- **Progress Tracking**: Visual feedback during conversion process
- **PWA Ready**: Install as a desktop/mobile app
- **Responsive Design**: Works perfectly on all devices
- **SEO Optimized**: Structured data, meta tags, and sitemap included

## 🚀 Supported Formats

### Input
- CSV (Comma-separated values)
- TXT (Tab-separated or custom delimiter)
- TSV (Tab-separated values)

### Output
- XLSX (Microsoft Excel format)

## 🛠️ Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Radix UI + shadcn/ui
- **Excel Processing**: SheetJS (xlsx)
- **Icons**: Lucide React
- **Font**: Inter (Google Fonts)

## 📦 Installation

1. Clone the repository:
```bash
git clone https://github.com/your-username/csv-excel-converter.git
cd csv-excel-converter
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🏗️ Project Structure

```
├── public/
│   ├── icons/           # PWA icons
│   ├── manifest.json    # PWA manifest
│   ├── robots.txt       # SEO robots file
│   ├── sitemap.xml      # SEO sitemap
│   ├── sw.js           # Service worker
│   └── browserconfig.xml # Microsoft config
├── src/
│   ├── app/            # Next.js 15 App Router
│   │   ├── layout.tsx  # Root layout with SEO
│   │   ├── page.tsx    # Homepage
│   │   ├── sitemap.ts  # Dynamic sitemap
│   │   ├── robots.ts   # Dynamic robots.txt
│   │   ├── en/         # English pages
│   │   └── fr/         # French pages
│   ├── components/     # React components
│   │   ├── converter.tsx    # Main converter
│   │   ├── ui/             # shadcn/ui components
│   │   ├── header.tsx      # Site header
│   │   ├── footer.tsx      # Site footer
│   │   └── language-switcher.tsx
│   ├── hooks/          # Custom React hooks
│   ├── lib/            # Utility functions
│   └── types/          # TypeScript types
├── next.config.ts      # Next.js configuration
├── tailwind.config.ts  # Tailwind configuration
└── package.json        # Dependencies
```

## 🔧 Configuration

### Environment Variables

Create a `.env.local` file in the root directory:

```env
# Optional: Google Analytics ID
NEXT_PUBLIC_GA_ID=your-ga-id

# Optional: Site URL for production
NEXT_PUBLIC_SITE_URL=https://your-domain.com
```

### Customization

1. **Update site URL**: Change the `baseUrl` constant in:
   - `src/app/layout.tsx`
   - `src/app/sitemap.ts`
   - `src/app/robots.ts`

2. **Modify branding**: Update colors, fonts, and styling in:
   - `tailwind.config.ts`
   - `src/app/globals.css`

3. **Add languages**: Create new folders in `src/app/[locale]/`

## 🌐 SEO Features

- **Structured Data**: Schema.org markup for better search visibility
- **Meta Tags**: Comprehensive Open Graph and Twitter Card support
- **Sitemap**: Auto-generated XML sitemap
- **Robots.txt**: Optimized for search engines
- **Canonical URLs**: Prevents duplicate content issues
- **Language Alternates**: Multi-language SEO support

## 📱 PWA Features

- **Offline Support**: Works without internet connection
- **Install Prompt**: Can be installed as native app
- **Background Sync**: Handles offline operations
- **Push Notifications**: Ready for future enhancements
- **App Shortcuts**: Quick access to main features

## 🔒 Security Features

- **Content Security Policy**: Prevents XSS attacks
- **HTTPS Only**: Secure connections enforced
- **No Data Storage**: Files processed client-side only
- **Input Validation**: Sanitized user inputs
- **Error Handling**: Graceful error management

## 🎯 Performance Optimizations

- **Code Splitting**: Automatic with Next.js
- **Image Optimization**: Next.js Image component
- **Font Optimization**: Google Fonts with display=swap
- **Gzip Compression**: Enabled in production
- **Caching**: Service worker and CDN caching
- **Bundle Size**: Optimized dependencies

## 📈 Analytics & Monitoring

The app is ready for analytics integration:

- Google Analytics 4
- Microsoft Clarity
- Vercel Analytics
- Custom event tracking

## 🚀 Deployment

### Vercel (Recommended)

1. Push to GitHub/GitLab
2. Connect to Vercel
3. Deploy automatically

### Docker

```bash
# Build image
docker build -t csv-excel-converter .

# Run container
docker run -p 3000:3000 csv-excel-converter
```

### Static Export

```bash
npm run build
npm run export
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Commit changes: `git commit -am 'Add feature'`
4. Push to branch: `git push origin feature-name`
5. Submit a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) - React framework
- [Tailwind CSS](https://tailwindcss.com/) - CSS framework
- [shadcn/ui](https://ui.shadcn.com/) - UI components
- [SheetJS](https://sheetjs.com/) - Excel processing
- [Lucide](https://lucide.dev/) - Icons

## 📞 Support

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/your-username/csv-excel-converter/issues)
- 💬 Discussions: [GitHub Discussions](https://github.com/your-username/csv-excel-converter/discussions)

---

**Made with ❤️ for the developer community**
