"use client";

import { useState, useRef, useCallback } from "react";
import * as XLSX from "xlsx";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import { Download, Upload, FileText, AlertCircle, CheckCircle2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

// تحسين: أنواع البيانات
interface ConversionState {
  isConverting: boolean;
  progress: number;
  rowCount: number;
  error: string | null;
}

export default function Converter() {
  const [csvText, setCsvText] = useState("");
  const [delimiter, setDelimiter] = useState(",");
  const [fileName, setFileName] = useState("converted-data");
  const [conversionState, setConversionState] = useState<ConversionState>({
    isConverting: false,
    progress: 0,
    rowCount: 0,
    error: null,
  });
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // تحسين: CSV parsing محسن
  const parseCSV = useCallback((text: string, delimiter: string) => {
    const lines = text.trim().split('\n');
    const result: string[][] = [];
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const row: string[] = [];
      let current = '';
      let inQuotes = false;
      
      for (let j = 0; j < line.length; j++) {
        const char = line[j];
        const nextChar = line[j + 1];
        
        if (char === '"' && inQuotes && nextChar === '"') {
          // Escaped quote
          current += '"';
          j++; // Skip next quote
        } else if (char === '"') {
          // Toggle quotes
          inQuotes = !inQuotes;
        } else if (char === delimiter && !inQuotes) {
          // End of field
          row.push(current.trim());
          current = '';
        } else {
          current += char;
        }
      }
      
      // Add the last field
      row.push(current.trim());
      result.push(row);
    }
    
    return result;
  }, []);

  // تحسين: معالجة الملفات
  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.name.match(/\.(csv|txt|tsv)$/i)) {
      toast({
        title: "Invalid file type",
        description: "Please select a CSV, TXT, or TSV file.",
        variant: "destructive",
      });
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setCsvText(content);
      
      // Auto-detect delimiter
      if (file.name.endsWith('.tsv')) {
        setDelimiter('\t');
      } else if (content.includes(';') && !content.includes(',')) {
        setDelimiter(';');
      }
      
      // Set filename without extension
      const nameWithoutExt = file.name.replace(/\.(csv|txt|tsv)$/i, '');
      setFileName(nameWithoutExt);
      
      toast({
        title: "File uploaded successfully",
        description: `Loaded ${file.name} with ${content.split('\n').length} rows.`,
      });
    };
    
    reader.onerror = () => {
      toast({
        title: "File read error",
        description: "Could not read the selected file.",
        variant: "destructive",
      });
    };
    
    reader.readAsText(file, 'UTF-8');
  }, [toast]);

  // تحسين: التحويل مع progress
  const handleConvert = useCallback(async () => {
    if (!csvText.trim()) {
      setConversionState(prev => ({ ...prev, error: "Please provide CSV data to convert." }));
      toast({
        title: "Input is empty",
        description: "Please paste your CSV data or upload a file first.",
        variant: "destructive",
      });
      return;
    }

    setConversionState({
      isConverting: true,
      progress: 0,
      rowCount: 0,
      error: null,
    });

    try {
      // Parse CSV with progress updates
      await new Promise(resolve => setTimeout(resolve, 100)); // Allow UI to update
      setConversionState(prev => ({ ...prev, progress: 25 }));

      const rows = parseCSV(csvText, delimiter);
      const rowCount = rows.length;
      
      if (rowCount === 0) {
        throw new Error("No data found in the input.");
      }

      setConversionState(prev => ({ ...prev, progress: 50, rowCount }));
      await new Promise(resolve => setTimeout(resolve, 100));

      // Create Excel workbook
      const worksheet = XLSX.utils.aoa_to_sheet(rows);
      
      // تحسين: Excel formatting
      const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');
      
      // Auto-width columns
      const colWidths = [];
      for (let C = range.s.c; C <= range.e.c; ++C) {
        let maxWidth = 0;
        for (let R = range.s.r; R <= range.e.r; ++R) {
          const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });
          const cell = worksheet[cellAddress];
          if (cell && cell.v) {
            const cellLength = cell.v.toString().length;
            maxWidth = Math.max(maxWidth, cellLength);
          }
        }
        colWidths.push({ width: Math.min(maxWidth + 2, 50) });
      }
      worksheet['!cols'] = colWidths;

      setConversionState(prev => ({ ...prev, progress: 75 }));
      await new Promise(resolve => setTimeout(resolve, 100));

      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");

      setConversionState(prev => ({ ...prev, progress: 90 }));
      await new Promise(resolve => setTimeout(resolve, 100));

      // Download file
      XLSX.writeFile(workbook, `${fileName}.xlsx`);
      
      setConversionState(prev => ({ ...prev, progress: 100 }));
      
      toast({
        title: "Conversion completed!",
        description: `Successfully converted ${rowCount} rows to Excel format.`,
      });

      // Reset progress after a delay
      setTimeout(() => {
        setConversionState({
          isConverting: false,
          progress: 0,
          rowCount: 0,
          error: null,
        });
      }, 2000);

    } catch (error) {
      console.error("Conversion error:", error);
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      
      setConversionState({
        isConverting: false,
        progress: 0,
        rowCount: 0,
        error: errorMessage,
      });
      
      toast({
        title: "Conversion Failed",
        description: `Error: ${errorMessage}. Please check your data format.`,
        variant: "destructive",
      });
    }
  }, [csvText, delimiter, fileName, parseCSV, toast]);

  const previewRows = csvText ? parseCSV(csvText, delimiter).slice(0, 3) : [];

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      {/* تحسين: Input Section */}
      <Card className="shadow-lg border-2">
        <CardHeader>
          <CardTitle className="text-2xl sm:text-3xl font-bold tracking-tight flex items-center gap-2">
            <FileText className="h-8 w-8 text-primary" />
            CSV to Excel Converter
          </CardTitle>
          <CardDescription className="text-base text-muted-foreground">
            Upload a file or paste your CSV/TXT data below. Our tool supports multilingual content and various delimiters.
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* تحسين: File Upload */}
          <div className="space-y-2">
            <Label htmlFor="file-upload" className="text-sm font-medium">
              Upload File (CSV, TXT, TSV)
            </Label>
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                onClick={() => fileInputRef.current?.click()}
                className="flex items-center gap-2"
              >
                <Upload className="h-4 w-4" />
                Choose File
              </Button>
              <Input
                id="file-upload"
                ref={fileInputRef}
                type="file"
                accept=".csv,.txt,.tsv"
                onChange={handleFileUpload}
                className="hidden"
              />
              <span className="text-sm text-muted-foreground">
                or paste data below
              </span>
            </div>
          </div>

          {/* تحسين: Settings */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="delimiter" className="text-sm font-medium">
                Delimiter
              </Label>
              <Select value={delimiter} onValueChange={setDelimiter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value=",">Comma (,)</SelectItem>
                  <SelectItem value=";">Semicolon (;)</SelectItem>
                  <SelectItem value="\t">Tab</SelectItem>
                  <SelectItem value="|">Pipe (|)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="filename" className="text-sm font-medium">
                Output Filename
              </Label>
              <Input
                id="filename"
                value={fileName}
                onChange={(e) => setFileName(e.target.value)}
                placeholder="converted-data"
              />
            </div>
          </div>

          {/* تحسين: Text Area */}
          <div className="space-y-2">
            <Label htmlFor="csv-input" className="text-sm font-medium">
              CSV/TXT Data
            </Label>
            <Textarea
              id="csv-input"
              value={csvText}
              onChange={(e) => setCsvText(e.target.value)}
              placeholder="id,title,description,subject_id,content_type,display_order&#10;1,مقدمة,هذا هو الفيديو الأول,101,video,1&#10;2,الفصل الثاني,هذا هو الفيديو الثاني,101,video,2"
              className="min-h-[300px] font-mono text-sm"
              aria-label="CSV Input Area"
            />
            {csvText && (
              <div className="text-sm text-muted-foreground">
                Detected {csvText.split('\n').length} rows
              </div>
            )}
          </div>

          {/* تحسين: Preview */}
          {previewRows.length > 0 && (
            <div className="space-y-2">
              <Label className="text-sm font-medium">Preview (First 3 rows)</Label>
              <div className="border rounded-md overflow-auto max-w-full">
                <table className="w-full text-sm">
                  <tbody>
                    {previewRows.map((row, i) => (
                      <tr key={i} className={i === 0 ? "bg-muted font-medium" : ""}>
                        {row.map((cell, j) => (
                          <td key={j} className="px-3 py-2 border-r border-b max-w-[200px] truncate">
                            {cell}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* تحسين: Progress */}
          {conversionState.isConverting && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Converting...</span>
                <span>{conversionState.progress}%</span>
              </div>
              <Progress value={conversionState.progress} className="w-full" />
              {conversionState.rowCount > 0 && (
                <div className="text-sm text-muted-foreground">
                  Processing {conversionState.rowCount} rows
                </div>
              )}
            </div>
          )}

          {/* تحسين: Error Display */}
          {conversionState.error && (
            <div className="flex items-center gap-2 p-3 bg-destructive/10 text-destructive rounded-md">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">{conversionState.error}</span>
            </div>
          )}
        </CardContent>
        
        <CardFooter>
          <Button
            onClick={handleConvert}
            disabled={!csvText.trim() || conversionState.isConverting}
            size="lg"
            className="w-full sm:w-auto"
          >
            {conversionState.isConverting ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Converting...
              </>
            ) : conversionState.progress === 100 ? (
              <>
                <CheckCircle2 className="mr-2 h-5 w-5" />
                Completed!
              </>
            ) : (
              <>
                <Download className="mr-2 h-5 w-5" />
                Convert & Download Excel File
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}

export { default as Converter } from "./converter";
