"use client";

import { useState, useRef, useCallback } from "react";
import * as XLSX from "xlsx";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import { Download, Upload, FileText, AlertCircle, CheckCircle2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

// Types de données
interface ConversionState {
  isConverting: boolean;
  progress: number;
  rowCount: number;
  error: string | null;
}

export function ConverterFR() {
  const [csvText, setCsvText] = useState("");
  const [delimiter, setDelimiter] = useState(",");
  const [fileName, setFileName] = useState("donnees-converties");
  const [conversionState, setConversionState] = useState<ConversionState>({
    isConverting: false,
    progress: 0,
    rowCount: 0,
    error: null,
  });
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Analyse CSV optimisée
  const parseCSV = useCallback((text: string, delimiter: string) => {
    const lines = text.trim().split('\n');
    const result: string[][] = [];
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const row: string[] = [];
      let current = '';
      let inQuotes = false;
      
      for (let j = 0; j < line.length; j++) {
        const char = line[j];
        const nextChar = line[j + 1];
        
        if (char === '"' && inQuotes && nextChar === '"') {
          // Guillemet échappé
          current += '"';
          j++; // Ignorer le prochain guillemet
        } else if (char === '"') {
          // Basculer les guillemets
          inQuotes = !inQuotes;
        } else if (char === delimiter && !inQuotes) {
          // Fin du champ
          row.push(current.trim());
          current = '';
        } else {
          current += char;
        }
      }
      
      // Ajouter le dernier champ
      row.push(current.trim());
      result.push(row);
    }
    
    return result;
  }, []);

  // Gestion des fichiers
  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.name.match(/\.(csv|txt|tsv)$/i)) {
      toast({
        title: "Type de fichier invalide",
        description: "Veuillez sélectionner un fichier CSV, TXT, ou TSV.",
        variant: "destructive",
      });
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setCsvText(content);
      
      // Détection automatique du délimiteur
      if (file.name.endsWith('.tsv')) {
        setDelimiter('\t');
      } else if (content.includes(';') && !content.includes(',')) {
        setDelimiter(';');
      }
      
      // Définir le nom du fichier sans extension
      const nameWithoutExt = file.name.replace(/\.(csv|txt|tsv)$/i, '');
      setFileName(nameWithoutExt);
      
      toast({
        title: "Fichier téléchargé avec succès",
        description: `Chargé ${file.name} avec ${content.split('\n').length} lignes.`,
      });
    };
    
    reader.onerror = () => {
      toast({
        title: "Erreur de lecture du fichier",
        description: "Impossible de lire le fichier sélectionné.",
        variant: "destructive",
      });
    };
    
    reader.readAsText(file, 'UTF-8');
  }, [toast]);

  // Conversion avec progression
  const handleConvert = useCallback(async () => {
    if (!csvText.trim()) {
      setConversionState(prev => ({ ...prev, error: "Veuillez fournir des données CSV à convertir." }));
      toast({
        title: "Entrée vide",
        description: "Veuillez coller vos données CSV ou télécharger un fichier d'abord.",
        variant: "destructive",
      });
      return;
    }

    setConversionState({
      isConverting: true,
      progress: 0,
      rowCount: 0,
      error: null,
    });

    try {
      // Analyser CSV avec mises à jour de progression
      await new Promise(resolve => setTimeout(resolve, 100)); // Permettre à l'UI de se mettre à jour
      setConversionState(prev => ({ ...prev, progress: 25 }));

      const rows = parseCSV(csvText, delimiter);
      const rowCount = rows.length;
      
      if (rowCount === 0) {
        throw new Error("Aucune donnée trouvée dans l'entrée.");
      }

      setConversionState(prev => ({ ...prev, progress: 50, rowCount }));
      await new Promise(resolve => setTimeout(resolve, 100));

      // Créer un classeur Excel
      const worksheet = XLSX.utils.aoa_to_sheet(rows);
      
      // Formatage Excel
      const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');
      
      // Largeur automatique des colonnes
      const colWidths = [];
      for (let C = range.s.c; C <= range.e.c; ++C) {
        let maxWidth = 0;
        for (let R = range.s.r; R <= range.e.r; ++R) {
          const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });
          const cell = worksheet[cellAddress];
          if (cell && cell.v) {
            const cellLength = cell.v.toString().length;
            maxWidth = Math.max(maxWidth, cellLength);
          }
        }
        colWidths.push({ width: Math.min(maxWidth + 2, 50) });
      }
      worksheet['!cols'] = colWidths;

      setConversionState(prev => ({ ...prev, progress: 75 }));
      await new Promise(resolve => setTimeout(resolve, 100));

      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "Feuille1");

      setConversionState(prev => ({ ...prev, progress: 90 }));
      await new Promise(resolve => setTimeout(resolve, 100));

      // Télécharger le fichier
      XLSX.writeFile(workbook, `${fileName}.xlsx`);
      
      setConversionState(prev => ({ ...prev, progress: 100 }));
      
      toast({
        title: "Conversion terminée !",
        description: `${rowCount} lignes converties avec succès au format Excel.`,
      });

      // Réinitialiser la progression après un délai
      setTimeout(() => {
        setConversionState({
          isConverting: false,
          progress: 0,
          rowCount: 0,
          error: null,
        });
      }, 2000);

    } catch (error) {
      console.error("Erreur de conversion:", error);
      const errorMessage = error instanceof Error ? error.message : "Une erreur inconnue s'est produite";
      
      setConversionState({
        isConverting: false,
        progress: 0,
        rowCount: 0,
        error: errorMessage,
      });
      
      toast({
        title: "Échec de la conversion",
        description: `Erreur: ${errorMessage}. Veuillez vérifier le format de vos données.`,
        variant: "destructive",
      });
    }
  }, [csvText, delimiter, fileName, parseCSV, toast]);

  const previewRows = csvText ? parseCSV(csvText, delimiter).slice(0, 3) : [];

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      {/* Section d'entrée */}
      <Card className="shadow-lg border-2">
        <CardHeader>
          <CardTitle className="text-2xl sm:text-3xl font-bold tracking-tight flex items-center gap-2">
            <FileText className="h-8 w-8 text-primary" />
            Convertisseur CSV vers Excel
          </CardTitle>
          <CardDescription className="text-base text-muted-foreground">
            Téléchargez un fichier ou collez vos données CSV/TXT ci-dessous. Notre outil prend en charge le contenu multilingue et divers délimiteurs.
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Téléchargement de fichier */}
          <div className="space-y-2">
            <Label htmlFor="file-upload" className="text-sm font-medium">
              Télécharger un fichier (CSV, TXT, TSV)
            </Label>
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                onClick={() => fileInputRef.current?.click()}
                className="flex items-center gap-2"
              >
                <Upload className="h-4 w-4" />
                Choisir un fichier
              </Button>
              <Input
                id="file-upload"
                ref={fileInputRef}
                type="file"
                accept=".csv,.txt,.tsv"
                onChange={handleFileUpload}
                className="hidden"
              />
              <span className="text-sm text-muted-foreground">
                ou collez les données ci-dessous
              </span>
            </div>
          </div>

          {/* Paramètres */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="delimiter" className="text-sm font-medium">
                Délimiteur
              </Label>
              <Select value={delimiter} onValueChange={setDelimiter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value=",">Virgule (,)</SelectItem>
                  <SelectItem value=";">Point-virgule (;)</SelectItem>
                  <SelectItem value="\t">Tabulation</SelectItem>
                  <SelectItem value="|">Barre verticale (|)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="filename" className="text-sm font-medium">
                Nom du fichier de sortie
              </Label>
              <Input
                id="filename"
                value={fileName}
                onChange={(e) => setFileName(e.target.value)}
                placeholder="donnees-converties"
              />
            </div>
          </div>

          {/* Zone de texte */}
          <div className="space-y-2">
            <Label htmlFor="csv-input" className="text-sm font-medium">
              Données CSV/TXT
            </Label>
            <Textarea
              id="csv-input"
              value={csvText}
              onChange={(e) => setCsvText(e.target.value)}
              placeholder="id,titre,description,sujet_id,type_contenu,ordre_affichage&#10;1,Introduction,Ceci est la première vidéo,101,video,1&#10;2,Chapitre deux,Ceci est la deuxième vidéo,101,video,2"
              className="min-h-[300px] font-mono text-sm"
              aria-label="Zone de saisie CSV"
            />
            {csvText && (
              <div className="text-sm text-muted-foreground">
                {csvText.split('\n').length} lignes détectées
              </div>
            )}
          </div>

          {/* Aperçu */}
          {previewRows.length > 0 && (
            <div className="space-y-2">
              <Label className="text-sm font-medium">Aperçu (3 premières lignes)</Label>
              <div className="border rounded-md overflow-auto max-w-full">
                <table className="w-full text-sm">
                  <tbody>
                    {previewRows.map((row, i) => (
                      <tr key={i} className={i === 0 ? "bg-muted font-medium" : ""}>
                        {row.map((cell, j) => (
                          <td key={j} className="px-3 py-2 border-r border-b max-w-[200px] truncate">
                            {cell}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Progression */}
          {conversionState.isConverting && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Conversion en cours...</span>
                <span>{conversionState.progress}%</span>
              </div>
              <Progress value={conversionState.progress} className="w-full" />
              {conversionState.rowCount > 0 && (
                <div className="text-sm text-muted-foreground">
                  Traitement de {conversionState.rowCount} lignes
                </div>
              )}
            </div>
          )}

          {/* Affichage des erreurs */}
          {conversionState.error && (
            <div className="flex items-center gap-2 p-3 bg-destructive/10 text-destructive rounded-md">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">{conversionState.error}</span>
            </div>
          )}
        </CardContent>
        
        <CardFooter>
          <Button
            onClick={handleConvert}
            disabled={!csvText.trim() || conversionState.isConverting}
            size="lg"
            className="w-full sm:w-auto"
          >
            {conversionState.isConverting ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Conversion en cours...
              </>
            ) : conversionState.progress === 100 ? (
              <>
                <CheckCircle2 className="mr-2 h-5 w-5" />
                Terminé !
              </>
            ) : (
              <>
                <Download className="mr-2 h-5 w-5" />
                Convertir et télécharger le fichier Excel
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}

