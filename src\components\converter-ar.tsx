"use client";

import { useState, useRef, useCallback } from "react";
import * as XLSX from "xlsx";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import { Download, Upload, FileText, AlertCircle, CheckCircle2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

// أنواع البيانات
interface ConversionState {
  isConverting: boolean;
  progress: number;
  rowCount: number;
  error: string | null;
}

export default function ConverterAR() {
  const [csvText, setCsvText] = useState("");
  const [delimiter, setDelimiter] = useState(",");
  const [fileName, setFileName] = useState("converted-data");
  const [conversionState, setConversionState] = useState<ConversionState>({
    isConverting: false,
    progress: 0,
    rowCount: 0,
    error: null,
  });
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // CSV parsing محسن
  const parseCSV = useCallback((text: string, delimiter: string) => {
    const lines = text.trim().split('\n');
    const result: string[][] = [];
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const row: string[] = [];
      let current = '';
      let inQuotes = false;
      
      for (let j = 0; j < line.length; j++) {
        const char = line[j];
        const nextChar = line[j + 1];
        
        if (char === '"' && inQuotes && nextChar === '"') {
          // Escaped quote
          current += '"';
          j++; // Skip next quote
        } else if (char === '"') {
          // Toggle quotes
          inQuotes = !inQuotes;
        } else if (char === delimiter && !inQuotes) {
          // End of field
          row.push(current.trim());
          current = '';
        } else {
          current += char;
        }
      }
      
      // Add the last field
      row.push(current.trim());
      result.push(row);
    }
    
    return result;
  }, []);

  // معالجة الملفات
  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.name.match(/\.(csv|txt|tsv)$/i)) {
      toast({
        title: "نوع ملف غير صالح",
        description: "يرجى اختيار ملف CSV أو TXT أو TSV.",
        variant: "destructive",
      });
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setCsvText(content);
      
      // Auto-detect delimiter
      if (file.name.endsWith('.tsv')) {
        setDelimiter('\t');
      } else if (content.includes(';') && !content.includes(',')) {
        setDelimiter(';');
      }
      
      // Set filename without extension
      const nameWithoutExt = file.name.replace(/\.(csv|txt|tsv)$/i, '');
      setFileName(nameWithoutExt);
      
      toast({
        title: "تم رفع الملف بنجاح",
        description: `تم تحميل ${file.name} مع ${content.split('\n').length} صف.`,
      });
    };
    
    reader.onerror = () => {
      toast({
        title: "خطأ في قراءة الملف",
        description: "تعذر قراءة الملف المحدد.",
        variant: "destructive",
      });
    };
    
    reader.readAsText(file, 'UTF-8');
  }, [toast]);

  // التحويل مع progress
  const handleConvert = useCallback(async () => {
    if (!csvText.trim()) {
      setConversionState(prev => ({ ...prev, error: "يرجى توفير بيانات CSV للتحويل." }));
      toast({
        title: "المدخلات فارغة",
        description: "يرجى لصق بيانات CSV الخاصة بك أو تحميل ملف أولاً.",
        variant: "destructive",
      });
      return;
    }

    setConversionState({
      isConverting: true,
      progress: 0,
      rowCount: 0,
      error: null,
    });

    try {
      // Parse CSV with progress updates
      await new Promise(resolve => setTimeout(resolve, 100)); // Allow UI to update
      setConversionState(prev => ({ ...prev, progress: 25 }));

      const rows = parseCSV(csvText, delimiter);
      const rowCount = rows.length;
      
      if (rowCount === 0) {
        throw new Error("لم يتم العثور على بيانات في المدخلات.");
      }

      setConversionState(prev => ({ ...prev, progress: 50, rowCount }));
      await new Promise(resolve => setTimeout(resolve, 100));

      // Create Excel workbook
      const worksheet = XLSX.utils.aoa_to_sheet(rows);
      
      // Excel formatting
      const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');
      
      // Auto-width columns
      const colWidths = [];
      for (let C = range.s.c; C <= range.e.c; ++C) {
        let maxWidth = 0;
        for (let R = range.s.r; R <= range.e.r; ++R) {
          const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });
          const cell = worksheet[cellAddress];
          if (cell && cell.v) {
            const cellLength = cell.v.toString().length;
            maxWidth = Math.max(maxWidth, cellLength);
          }
        }
        colWidths.push({ width: Math.min(maxWidth + 2, 50) });
      }
      worksheet['!cols'] = colWidths;

      setConversionState(prev => ({ ...prev, progress: 75 }));
      await new Promise(resolve => setTimeout(resolve, 100));

      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");

      setConversionState(prev => ({ ...prev, progress: 90 }));
      await new Promise(resolve => setTimeout(resolve, 100));

      // Download file
      XLSX.writeFile(workbook, `${fileName}.xlsx`);
      
      setConversionState(prev => ({ ...prev, progress: 100 }));
      
      toast({
        title: "اكتمل التحويل!",
        description: `تم تحويل ${rowCount} صف إلى تنسيق Excel بنجاح.`,
      });

      // Reset progress after a delay
      setTimeout(() => {
        setConversionState({
          isConverting: false,
          progress: 0,
          rowCount: 0,
          error: null,
        });
      }, 2000);

    } catch (error) {
      console.error("خطأ في التحويل:", error);
      const errorMessage = error instanceof Error ? error.message : "حدث خطأ غير معروف";
      
      setConversionState({
        isConverting: false,
        progress: 0,
        rowCount: 0,
        error: errorMessage,
      });
      
      toast({
        title: "فشل التحويل",
        description: `خطأ: ${errorMessage}. يرجى التحقق من تنسيق البيانات الخاصة بك.`,
        variant: "destructive",
      });
    }
  }, [csvText, delimiter, fileName, parseCSV, toast]);

  const previewRows = csvText ? parseCSV(csvText, delimiter).slice(0, 3) : [];

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6" dir="rtl">
      {/* Input Section */}
      <Card className="shadow-lg border-2">
        <CardHeader>
          <CardTitle className="text-2xl sm:text-3xl font-bold tracking-tight flex items-center gap-2">
            <FileText className="h-8 w-8 text-primary" />
            محول CSV إلى Excel
          </CardTitle>
          <CardDescription className="text-base text-muted-foreground">
            قم بتحميل ملف أو لصق بيانات CSV/TXT أدناه. تدعم أداتنا المحتوى متعدد اللغات والفواصل المختلفة.
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* File Upload */}
          <div className="space-y-2">
            <Label htmlFor="file-upload" className="text-sm font-medium">
              تحميل ملف (CSV، TXT، TSV)
            </Label>
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                onClick={() => fileInputRef.current?.click()}
                className="flex items-center gap-2"
              >
                <Upload className="h-4 w-4 transform-rtl" />
                اختر ملفًا
              </Button>
              <Input
                id="file-upload"
                ref={fileInputRef}
                type="file"
                accept=".csv,.txt,.tsv"
                onChange={handleFileUpload}
                className="hidden"
              />
              <span className="text-sm text-muted-foreground">
                أو الصق البيانات أدناه
              </span>
            </div>
          </div>

          {/* Settings */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="delimiter" className="text-sm font-medium">
                الفاصل
              </Label>
              <Select value={delimiter} onValueChange={setDelimiter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value=",">فاصلة (,)</SelectItem>
                  <SelectItem value=";">فاصلة منقوطة (;)</SelectItem>
                  <SelectItem value="\t">مسافة جدولية (Tab)</SelectItem>
                  <SelectItem value="|">عمودي (|)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="filename" className="text-sm font-medium">
                اسم ملف الإخراج
              </Label>
              <Input
                id="filename"
                value={fileName}
                onChange={(e) => setFileName(e.target.value)}
                placeholder="converted-data"
              />
            </div>
          </div>

          {/* Text Area */}
          <div className="space-y-2">
            <Label htmlFor="csv-input" className="text-sm font-medium">
              بيانات CSV/TXT
            </Label>
            <Textarea
              id="csv-input"
              value={csvText}
              onChange={(e) => setCsvText(e.target.value)}
              placeholder="id,title,description,subject_id,content_type,display_order&#10;1,مقدمة,هذا هو الفيديو الأول,101,video,1&#10;2,الفصل الثاني,هذا هو الفيديو الثاني,101,video,2"
              className="min-h-[300px] font-mono text-sm"
              aria-label="CSV Input Area"
            />
            {csvText && (
              <div className="text-sm text-muted-foreground">
                تم اكتشاف {csvText.split('\n').length} صف
              </div>
            )}
          </div>

          {/* Preview */}
          {previewRows.length > 0 && (
            <div className="space-y-2">
              <Label className="text-sm font-medium">معاينة (أول 3 صفوف)</Label>
              <div className="border rounded-md overflow-auto max-w-full">
                <table className="w-full text-sm">
                  <tbody>
                    {previewRows.map((row, i) => (
                      <tr key={i} className={i === 0 ? "bg-muted font-medium" : ""}>
                        {row.map((cell, j) => (
                          <td key={j} className="px-3 py-2 border-l border-b max-w-[200px] truncate">
                            {cell}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Progress */}
          {conversionState.isConverting && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>جاري التحويل...</span>
                <span>{conversionState.progress}%</span>
              </div>
              <Progress value={conversionState.progress} className="w-full" />
              {conversionState.rowCount > 0 && (
                <div className="text-sm text-muted-foreground">
                  معالجة {conversionState.rowCount} صف
                </div>
              )}
            </div>
          )}

          {/* Error Display */}
          {conversionState.error && (
            <div className="flex items-center gap-2 p-3 bg-destructive/10 text-destructive rounded-md">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">{conversionState.error}</span>
            </div>
          )}
        </CardContent>
        
        <CardFooter>
          <Button
            onClick={handleConvert}
            disabled={!csvText.trim() || conversionState.isConverting}
            size="lg"
            className="w-full sm:w-auto"
          >
            {conversionState.isConverting ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white ml-2"></div>
                جاري التحويل...
              </>
            ) : conversionState.progress === 100 ? (
              <>
                <CheckCircle2 className="ml-2 h-5 w-5" />
                تم الإكمال!
              </>
            ) : (
              <>
                <Download className="ml-2 h-5 w-5" />
                تحويل وتنزيل ملف Excel
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}

export { default as ConverterAR } from "./converter-ar";